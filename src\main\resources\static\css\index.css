/* Page-specific styles for index.html */
body {
    max-width: 800px;
}

.card {
    transition: transform 0.3s ease;
}
.card:hover {
    transform: translateY(-5px);
}
.card h2 {
    margin-top: 0;
}
.card-link {
    text-decoration: none;
    color: inherit;
    display: block;
}
.button {
    margin-top: 10px;
}


  .dashboard {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 1rem;
  }

  .intro {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .logo-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 1rem;
  }

  .logo-row img {
    height: 60px;
    object-fit: contain;
  }

  .section {
    margin-top: 3rem;
  }

  .section h2 {
    margin-bottom: 0.5rem;
  }

  .cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
  }


  .card  {
    /* margin-top: 1rem; */
    display: inline-block;
    /* background-color: #0073e6;
    color: white; */
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
  }


  .logo-row {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.logo-row img {
    height: 60px;         /* Set desired uniform height */
    object-fit: contain;  /* Maintain aspect ratio */
    max-width: 160px;     /* Optional: limits width to prevent huge logos */
    padding: 0.5rem;      /* Optional: adds some breathing room */
}