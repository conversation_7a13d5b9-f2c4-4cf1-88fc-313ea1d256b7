<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FHIR Prior Authorization Dashboard</title>
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/index.css" />
</head>

<body>
    <div class="nav">
        <a href="/">Home</a>
        <a href="/auth.html">Authentication Test</a>
        <a href="/submit.html">Submit Prior Auth</a>
        <a href="/poll.html">Poll Status</a>
        <a href="/convert.html">FHIR Converter</a>
        <a href="/api-tracker.html">API Tracker</a>
    </div>


    <h1>FHIR Prior Authorization Dashboard</h1>

    <p>
        This dashboard demonstrates how to integrate with the <strong>Availity API</strong> to submit and manage
        <strong>FHIR-based Prior Authorization</strong> requests. It is designed for healthcare developers and
        integrators looking to streamline their claims workflows using modern standards.
    </p>


    <div class="section">
        <div class="cards">
            <div class="card">
                <h3>Authentication Test</h3>
                <p>Verify connectivity with the Availity API using OAuth 2.0.</p>
                <a class="button" href="/auth.html">Start Test</a>
            </div>
            <div class="card">
                <h3>Submit Prior Authorization</h3>
                <p>Create and submit a FHIR Claim request to Availity.</p>
                <a class="button" href="/submit.html">Submit Request</a>
            </div>
            <div class="card">
                <h3>Poll Status</h3>
                <p>Track the status of your authorization requests.</p>
                <a class="button" href="/poll.html">Check Status</a>
            </div>
            <div class="card">
                <h3>FHIR Converter</h3>
                <p>Convert between FHIR and Availity JSON formats.</p>
                <a class="button" href="/convert.html">Convert Now</a>
            </div>
        </div>
    </div>

    <div class="intro">

        <div class="logo-row">
            <img src="availity-logo.webp" alt="Availity Logo" />
            <img src="FHIR_logo.png" alt="FHIR Logo" />
            <img src="athenahealth-logo.png" alt="Provider Logo" />

        </div>
    </div>

    <div class="section">
        <h2>What is Prior Authorization?</h2>
        <p>
            Prior Authorization is a process by which healthcare providers obtain approval from a payer before
            delivering a service
            to ensure reimbursement. This app simulates how this process works with FHIR Claims and Availity APIs.
        </p>
    </div>

</body>

</html>