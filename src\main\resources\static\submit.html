<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Prior Authorization</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/submit.css">
</head>
<body>
    <div class="nav">
        <a href="/">Home</a>
        <a href="/auth.html">Authentication Test</a>
        <a href="/submit.html">Submit Prior Auth</a>
        <a href="/poll.html">Poll Status</a>
        <a href="/convert.html">FHIR Converter</a>
        <a href="/api-tracker.html">API Tracker</a>
    </div>

    <h1>Submit Prior Authorization</h1>

    <div class="card">
        <h2>Test Submission</h2>
        <p>Click the button below to submit a test prior authorization request to Availity:</p>
        <button id="submitAuth">Submit Prior Auth</button>
        <span id="loading">Loading...</span>
    </div>

    <div class="card" id="request-details-card">
        <h2>Request Details</h2>
        <div class="tabs">
            <button class="tab-button active" data-tab="fhir-request">FHIR</button>
            <button class="tab-button" data-tab="availity-request">Availity (Converted)</button>
        </div>
        <div class="tab-content">
            <div id="fhir-request-tab" class="tab-pane active">
                <details id="fhir-request-viewer" open>
                    <summary>FHIR Request</summary>
                    <pre id="fhir-request-content"></pre>
                </details>
            </div>
            <div id="availity-request-tab" class="tab-pane">
                <details id="availity-request-viewer" open>
                    <summary>Availity Request</summary>
                    <pre id="availity-request-content"></pre>
                </details>
            </div>
        </div>
    </div>

    <div id="result"></div>

    <script src="js/submit.js"></script>
</body>
</html>
