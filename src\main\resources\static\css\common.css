/* Common styles shared across all pages */
body {
    font-family: Arial, sans-serif;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1 {
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.card {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.success {
    border-left: 5px solid #4CAF50;
}

.error {
    border-left: 5px solid #f44336;
}

button, .button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 4px;
}

button:hover, .button:hover {
    background-color: #45a049;
}

.nav {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.nav a {
    text-decoration: none;
    color: #4CAF50;
    text-decoration: none;
}

.nav a:hover {
    text-decoration: underline;
}


.nav a:hover {
    text-decoration: underline;
}