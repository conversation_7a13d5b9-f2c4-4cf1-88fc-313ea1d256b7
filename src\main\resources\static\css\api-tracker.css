/* Styles for API Tracker page */
body {
    max-width: 1200px;
    margin: 0 auto;
}

/* Filter controls */
.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.filter-group label {
    font-weight: bold;
    margin-bottom: 5px;
}

.filter-group select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Buttons */
.primary-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    align-self: flex-end;
    margin-top: auto;
}

.primary-btn:hover {
    background-color: #45a049;
}

.danger-btn {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    align-self: flex-end;
    margin-top: auto;
}

.danger-btn:hover {
    background-color: #d32f2f;
}

/* Table styles */
.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

tr {
    margin-bottom: 8px;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

th {
    background-color: #f5f5f5;
    font-weight: bold;
}

tr:hover {
    background-color: #f9f9f9;
}

/* Action buttons in table */
.action-btn {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 5px;
}

.action-btn:hover {
    background-color: #0b7dda;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    border-bottom: none;
}

.pagination button {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    padding: 8px 15px;
    margin: 0 5px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    font-weight: normal;
}

.pagination button:hover {
    background-color: #e8e8e8;
    border-color: #bbb;
}

.pagination button.active {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
    font-weight: bold;
}

.pagination button:disabled {
    background-color: #f5f5f5;
    color: #aaa;
    cursor: not-allowed;
    border-color: #ddd;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 800px;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
}

/* JSON viewer */
.json-viewer {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    overflow: auto;
    max-height: 300px;
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-all;
}

/* Status indicators */
.status-success {
    color: #4CAF50;
    font-weight: bold;
}

.status-error {
    color: #f44336;
    font-weight: bold;
}

.status-pending {
    color: #2196F3;
    font-weight: bold;
}

/* Active navigation link */
.nav a.active {
    font-weight: bold;
    text-decoration: underline;
}
