/* Page-specific styles for convert.html */
body {
    max-width: 1200px;
}

.info {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 3px;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.edit-button {
    background-color: #2196F3;
}

.edit-button:hover {
    background-color: #0b7dda;
}

.convert-button {
    background-color: #ff9800;
}

.convert-button:hover {
    background-color: #e68a00;
}

#loading {
    display: none;
    margin-left: 10px;
}

.container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.json-container {
    flex: 1;
    min-width: 300px;
}

.json-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.json-header h2 {
    margin: 0;
}

.button-container {
    display: flex;
    gap: 5px;
}

.json-editor,
.json-viewer {
    width: 100%;
    height: 500px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.4;
    overflow: auto;
    box-sizing: border-box;
}

.json-editor {
    resize: vertical;
}

.json-viewer pre {
    margin: 0;
    white-space: pre-wrap;
}

.status-message {
    padding: 10px;
    margin-top: 20px;
    border-radius: 4px;
    display: none;
}

.status-message.success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.status-message.error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}


