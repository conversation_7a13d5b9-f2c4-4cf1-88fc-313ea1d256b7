<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FHIR and Availity API Converter</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/convert.css">
</head>

<body>

    <div class="nav">
        <a href="/">Home</a>
        <a href="/auth.html">Authentication Test</a>
        <a href="/submit.html">Submit Prior Auth</a>
        <a href="/poll.html">Poll Status</a>
        <a href="/convert.html">FHIR Converter</a>
        <a href="/api-tracker.html">API Tracker</a>
    </div>

    <h1>FHIR and Availity API Converter</h1>

    <div class="card">
        <p>Convert between FHIR Claim resources and Availity API format. Edit either JSON and click the "Convert" button
            to convert in the desired direction.</p>

        <div class="container">
            <div class="json-container">
                <div class="json-header">
                    <h2>FHIR</h2>
                    <div class="button-container">
                        <button id="loadSampleFhir">Load Sample</button>
                        <button id="editFhir" class="edit-button">Edit</button>
                        <button id="convertToAvailityJson" class="convert-button">Convert →</button>
                    </div>
                </div>
                <textarea id="fhirJson" class="json-editor" spellcheck="false"></textarea>
                <div id="fhirJsonViewer" class="json-viewer" style="display: none;"></div>
            </div>

            <div class="json-container">
                <div class="json-header">
                    <h2>Availity API</h2>
                    <div class="button-container">
                        <button id="convertToFhirJson" class="convert-button">← Convert</button>
                        <button id="editAvailityJson" class="edit-button">Edit</button>
                        <button id="loadSampleAvailityJson">Load Sample</button>
                    </div>
                </div>
                <textarea id="availityJsonEditor" class="json-editor" spellcheck="false"
                    style="display: none;"></textarea>
                <div id="availityJsonViewer" class="json-viewer"></div>
            </div>
        </div>

        <div id="statusMessage" class="status-message"></div>
    </div>

    <!-- Include external JavaScript file -->
    <script src="js/convert.js"></script>
</body>

</html>