{"resourceType": "<PERSON><PERSON><PERSON>", "status": "active", "type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/claim-type", "code": "professional"}]}, "use": "preauthorization", "created": "2022-09-01", "provider": {"identifier": {"system": "http://hl7.org/fhir/sid/us-npi", "value": "**********"}, "display": "TEST PROVIDERONE"}, "insurer": {"identifier": {"system": "http://availity.com/payer-id", "value": "99999"}}, "patient": {"identifier": {"system": "http://example.org/members", "value": "NCF103T99937"}, "display": "TEST PATIENTONE"}, "priority": {"coding": [{"code": "normal"}]}, "diagnosis": [{"sequence": 1, "diagnosisCodeableConcept": {"coding": [{"system": "http://hl7.org/fhir/sid/icd-10", "code": "A52.00"}]}}], "procedure": [{"sequence": 1, "date": "2022-09-02", "procedureCodeableConcept": {"coding": [{"system": "http://www.ama-assn.org/go/cpt", "code": "99242"}]}}]}